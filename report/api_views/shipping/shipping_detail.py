from rest_framework.response import Response
from rest_framework import serializers
from django.db.models import Sum, Count, Avg, Q
from django.db.models.functions import TruncDate
from datetime import datetime
from core.serializers.primary_fields import (
    CompanyBranchPkRelatedField,
    CompanyUserPkRelatedField,
)
from order.models import OrderShipping
from report.report_framework.serializer_fields import Char<PERSON>ield
from shipping.models import DeliveryProvider
from report.permissions import ReportShippingDetailPermission
from report.report_framework.serializers import FilterSerializer
from report.report_framework.utils import get_datetime_range
from report.report_framework.viewsets import Colors, ReportViewSet
from report.report_framework.interface import IReportLogic
from util.serializer_fields import DateTimeRangeField


class ShippingDetailFilterSerializer(FilterSerializer):
    create_at = DateTimeRangeField(
        required=False,
        label="ช่วงวันที่สร้าง",
        default=get_datetime_range("this_month"),
    )
    schedule_at = DateTimeRangeField(
        required=False,
        label="ช่วงวันที่จองขนส่ง",
    )
    actual_receiving_datetime = DateTimeRangeField(
        required=False,
        label="ช่วงวันที่ขนส่งมารับของ",
    )
    create_by__in = CompanyUserPkRelatedField(
        required=False, many=True, label="สร้างโดย"
    )
    for_order__pickup_branch__in = CompanyBranchPkRelatedField(
        required=False,
        many=True,
        label="ส่งจากสาขา",
    )
    delivery_service_type = serializers.ChoiceField(
        choices=[
            "MOTORBIKE",
            "CAR",
        ],
        required=False,
        label="ประเภทสถานที่",
    )
    delivery_status = serializers.ChoiceField(
        choices=OrderShipping.DELIVERY_STATUS,
        required=False,
        label="สถานะ",
    )

    class Meta:
        search_fields = [
            "job_id",
            "order__order_number",
            "end_address__name",
            "end_address__phone",
            "end_address__formatted_address",
            "driver_detail__name",
            "driver_detail__phone",
        ]


class ShippingDashboardFilterSerializer(FilterSerializer):
    create_at = DateTimeRangeField(
        required=False,
        label="ช่วงวันที่",
        default=get_datetime_range("today"),
    )
    for_order__pickup_branch__in = CompanyBranchPkRelatedField(
        required=False,
        many=True,
        label="สาขา",
    )
    create_by__in = CompanyUserPkRelatedField(required=False, many=True, label="ผู้ใช้งาน")
    delivery_status = serializers.ChoiceField(
        choices=OrderShipping.DELIVERY_STATUS,
        required=False,
        label="สถานะ",
    )
    delivery_provider__in = serializers.PrimaryKeyRelatedField(
        queryset=DeliveryProvider.objects.all(),
        required=False,
        many=True,
        label="ขนส่ง",
    )

    class Meta:
        search_fields = [
            "job_id",
            "for_order__order_number",
            "end_address__name",
            "end_address__phone",
            "delivery_provider__name",
        ]


class ShippingDetailReportData(ReportViewSet):
    permission_classes = [ReportShippingDetailPermission]

    report_headers = [
        {"title": "หมายเลขงาน", "key": "job_id"},
        {"title": "ออเดอร์", "key": "for_order__order_number"},
        {"title": "ขนส่ง", "key": "delivery_provider__name"},
        {"title": "ประเภทยานพาหนะ", "key": "delivery_service_type"},
        {"title": "วันที่ส่ง", "key": "schedule_at", "type": "datetime"},
        {"title": "วันที่ส่งจริง", "key": "actual_receiving_datetime", "type": "datetime"},
        {"title": "สถานะ", "key": "delivery_status"},
        {"title": "ลิ้งค์ติดตาม", "key": "tracking_link"},
        {"title": "ค่าส่ง", "key": "delivery_cost", "type": "currency"},
        {"title": "ส่งจากสาขา", "key": "for_order__pickup_branch__name"},
        {"title": "ชื่อผู้รับ", "key": "end_address__name"},
        {"title": "เบอร์โทรผู้รับ", "key": "end_address__phone"},
        {
            "title": "ที่อยู่ผู้รับ",
            "key": "end_address__formatted_address",
            "minWidth": "350px",
        },
        {"title": "ชื่อคนขับ", "key": "driver_detail__name"},
        {"title": "เบอร์โทรคนขับ", "key": "driver_detail__phone"},
        {"title": "สร้างโดย", "key": "create_by__first_name"},
        {"title": "สร้างเมื่อ", "key": "create_at", "type": "datetime"},
    ]

    queryset = OrderShipping.objects.all()
    filter_serializer = ShippingDetailFilterSerializer

    def get_report_logic(self, queryset, context):
        return queryset.values(
            "job_id",
            "order",
            "for_order__order_number",
            "delivery_provider__name",
            "delivery_service_type",
            "schedule_at",
            "actual_receiving_datetime",
            "delivery_status",
            "tracking_link",
            "delivery_cost",
            "for_order__pickup_branch__name",
            "end_address__name",
            "end_address__phone",
            "end_address__formatted_address",
            "driver_detail__name",
            "driver_detail__phone",
            "create_by__first_name",
            "create_at",
        )


class AggregateReport(serializers.Serializer):
    total_delivery_fee = CharField(
        label="ค่าส่งเก็บจากลูกค้า",
        suffix="บาท",
        color=Colors.BLUE,
    )
    total_shipments = CharField(
        label="จำนวนการจัดส่ง",
        suffix="ออเดอร์",
        color=Colors.GREEN,
    )
    # total_distance = CharField(
    #     label="ระยะทางรวม",
    #     suffix="เมตร",
    #     color=Colors.BLUE,
    # )
    avg_cost = CharField(
        label="ค่าเฉลี่ยค่าส่ง",
        suffix="ออเดอร์",
        color=Colors.GREEN,
    )
    total_cost = CharField(
        label="ต้นทุนจัดส่ง",
        suffix="บาท",
        color=Colors.BLUE,
    )


class ShippingDashboardReportData(ReportViewSet):
    permission_classes = [ReportShippingDetailPermission]
    queryset = OrderShipping.objects.all()
    filter_serializer = ShippingDashboardFilterSerializer
    aggregate_serializer = AggregateReport
    report_headers = [
        {"title": "ขนส่ง", "key": "delivery_provider__name"},
        {"title": "จำนวนการส่ง", "key": "total_orders", "type": "number"},
        {"title": "ค่าส่ง", "key": "total_cost", "type": "currency"},
    ]

    def get_aggregate_logic(self, queryset, *args, **kwargs) -> dict:
        """Generate aggregate data for shipping dashboard"""

        # Status counts
        status_counts = {}
        for status_code, status_label in OrderShipping.DELIVERY_STATUS:
            count = queryset.filter(delivery_status=status_code).count()
            status_counts[status_code] = count

        # Calculate summary statistics
        summary = queryset.exclude(
            delivery_status=OrderShipping.DELIVERY_VOID
        ).aggregate(
            total_cost=Sum("delivery_cost"),
            total_shipments=Count("id"),
            # total_distance=Sum("distance"),
            avg_cost=Avg("delivery_cost"),
            total_delivery_fee=Sum("for_order__delivery_fee"),
        )

        return {
            "total_delivery_fee": summary["total_delivery_fee"] or 0,
            "total_cost": summary["total_cost"] or 0,
            "total_shipments": summary["total_shipments"] or 0,
            # "total_distance": summary["total_distance"] or 0,
            "avg_cost": summary["avg_cost"] or 0,
            "status_counts": status_counts,
        }

    def get_report_logic(self, queryset, *args, **kwargs) -> dict:
        """Generate daily shipping data for charts"""
        # Provider summary
        provider_data = (
            queryset.exclude(delivery_status=OrderShipping.DELIVERY_VOID)
            .values("delivery_provider__name")
            .annotate(total_orders=Count("id"), total_cost=Sum("delivery_cost"))
            .order_by("-total_orders")
        )

        return provider_data

    def chart_data(self, request):
        """Return chart.js compatible data for daily shipping stacked bar chart by provider"""

        params = request.data

        # validate data
        validator = self.get_validate_serializer()
        validated_filter = validator(data=params, context={"request": request})
        validated_filter.is_valid(raise_exception=True)

        # Get filtered queryset
        raw_queryset = self.get_queryset()
        queryset = self.filtered_queryset(
            queryset=raw_queryset,
            params=validated_filter,
        )

        # Get daily data grouped by provider
        daily_provider_data = (
            queryset.exclude(delivery_status=OrderShipping.DELIVERY_VOID)
            .annotate(date=TruncDate("create_at"))
            .values("date", "delivery_provider__name")
            .annotate(
                total_cost=Sum("delivery_cost"),
            )
            .order_by("date", "delivery_provider__name")
        )

        # Organize data by date and provider
        dates_dict = {}
        providers_set = set()

        for item in daily_provider_data:
            if item["date"]:
                date_str = item["date"].strftime("%d/%m/%Y")
                provider_name = item["delivery_provider__name"] or "ไม่ระบุ"
                cost = float(item["total_cost"] or 0)

                if date_str not in dates_dict:
                    dates_dict[date_str] = {}

                dates_dict[date_str][provider_name] = cost
                providers_set.add(provider_name)

        # Sort dates and providers
        sorted_dates = sorted(
            dates_dict.keys(), key=lambda x: datetime.strptime(x, "%d/%m/%Y")
        )
        sorted_providers = sorted(list(providers_set))

        # Generate colors for providers
        colors = [
            "rgba(54, 162, 235, 0.8)",  # Blue
            "rgba(255, 99, 132, 0.8)",  # Red
            "rgba(255, 206, 86, 0.8)",  # Yellow
            "rgba(75, 192, 192, 0.8)",  # Teal
            "rgba(153, 102, 255, 0.8)",  # Purple
            "rgba(255, 159, 64, 0.8)",  # Orange
            "rgba(199, 199, 199, 0.8)",  # Grey
            "rgba(83, 102, 255, 0.8)",  # Indigo
        ]

        # Create datasets for each provider
        datasets = []
        for i, provider in enumerate(sorted_providers):
            provider_data = []
            for date in sorted_dates:
                cost = dates_dict[date].get(provider, 0)
                provider_data.append(cost)

            # Only add dataset if it has at least one non-zero value
            if any(value > 0 for value in provider_data):
                datasets.append(
                    {
                        "label": provider,
                        "data": provider_data,
                        "backgroundColor": colors[i % len(colors)],
                        "borderColor": colors[i % len(colors)].replace("0.8", "1"),
                        "borderWidth": 1,
                    }
                )

        # Calculate daily totals for labels
        daily_totals = []
        for date in sorted_dates:
            total = sum(dates_dict[date].values())
            daily_totals.append(total)

        # Format labels to show only day/month
        labels = [
            date.split("/")[0] + "/" + date.split("/")[1] for date in sorted_dates
        ]

        chart_data = {
            "labels": labels,
            "datasets": datasets,
            "daily_totals": daily_totals,  # For displaying total labels
        }

        return Response({"chart_data": chart_data})
