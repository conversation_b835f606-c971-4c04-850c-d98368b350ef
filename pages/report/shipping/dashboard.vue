<template>
  <div>
    <div class="d-flex align-center">
      <h1 class="font-weight-medium">Dashboard การจัดส่ง</h1>
      <v-spacer></v-spacer>

      <!-- Default Filter Button -->
      <filter-panel
        v-model:filter-data="filter_data"
        schema-url="/report/shipping_dashboard/filter-schema/"
        @submit-form="fetchReport($event)"
      ></filter-panel>
    </div>

    <v-row dense>
      <!-- Summary Cards -->
      <v-col cols="12">
        <aggregate-report
          ref="aggregate_report_ref"
          api-url="/report/shipping_dashboard/aggregated-report/"
          :filter="filter_data"
        />
        <!-- :layouts="aggregateLayouts" -->
      </v-col>

      <!-- Status Flow Chart -->
      <v-col cols="12">
        <v-card rounded="lg" elevation="2">
          <v-card-title>สถานะการจัดส่ง</v-card-title>
          <v-card-text>
            <shipping-status-flow
              :status-data="statusData"
              @status-click="filterByStatus"
            />
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Daily Chart -->
      <v-col cols="12" md="6">
        <stacked-bar-chart
          ref="daily_chart_ref"
          title="สรุปรายวัน"
          api-url="/report/shipping_dashboard/chart-data/"
          :filter="filter_data"
          :height="350"
          :options="dailyChartOptions"
          @data-loaded="onDailyDataLoaded"
        />
      </v-col>

      <!-- Provider Summary -->
      <v-col cols="12" md="6">
        <v-card rounded="lg" elevation="2" title="สรุปตามขนส่ง" height="100%">
          <super-table-report
            class="elevation-0"
            ref="provider_report_ref"
            :filter="filter_data"
            height="265px"
            api-url="/report/shipping_dashboard/table-report/"
          ></super-table-report>
        </v-card>
      </v-col>

      <!-- Detailed Table -->
      <v-col cols="12">
        <v-card rounded="lg" elevation="2" title="รายละเอียดการจัดส่ง">
          <super-table-report
            ref="table_report_ref"
            :filter="filter_data"
            api-url="/report/shipping_detail/table-report/"
          ></super-table-report>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  permission: "can_view_report_shipping_detail",
});

const { sfetch } = useServerFetch();

// Report Refs
const aggregate_report_ref = ref();
const daily_chart_ref = ref();
const table_report_ref = ref();
const provider_report_ref = ref();

// Filter
const filter_data = ref<any>({});

// Data
const statusData = ref<any>({});
const providerData = ref<any>([]);

// Chart options
const dailyChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: true,
    },
    y: {
      stacked: true,
      beginAtZero: true,
      title: {
        display: true,
        text: 'ค่าส่ง (บาท)'
      }
    }
  },
  plugins: {
    legend: {
      display: true,
      position: 'top'
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      callbacks: {
        footer: function(tooltipItems: any) {
          let sum = 0;
          tooltipItems.forEach(function(tooltipItem: any) {
            sum += tooltipItem.parsed.y;
          });
          return 'รวม: ' + sum.toLocaleString() + ' บาท';
        }
      }
    }
  }
});


function fetchReport(data: any) {
  filter_data.value = data;

  // Fetch all reports
  [
    aggregate_report_ref.value,
    daily_chart_ref.value,
    table_report_ref.value,
    provider_report_ref.value,
  ].forEach((item: any) => {
    if (item) {
      item.fetchReport();
    }
  });

  // Fetch additional data for custom components
  fetchStatusData();
}

async function fetchStatusData() {
  try {
    const response: any = await sfetch('/report/shipping_dashboard/aggregated-report/', {
      method: 'POST',
      body: filter_data.value
    });
    statusData.value = response.data?.status_counts || {};
  } catch (error) {
    console.error('Error fetching status data:', error);
  }
}

function onDailyDataLoaded(_data: any) {
  // Handle daily chart data if needed
}

function filterByStatus(status: string) {
  filter_data.value = {
    ...filter_data.value,
    delivery_status: status
  };
  fetchReport(filter_data.value);
}


onMounted(() => {
  fetchReport({});
});
</script>

<style scoped></style>
