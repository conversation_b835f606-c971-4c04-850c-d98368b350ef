<template>
  <div class="status-flow-container">
    <div class="status-flow">
      <template v-for="(status, index) in statusFlow" :key="status.code">
        <div class="status-item" @click="$emit('status-click', status.code)">
          <!-- Status Circle -->
          <div
            class="status-circle"
            :class="[`bg-${status.color}`, { 'clickable': true }]"
          >
            <div class="status-count">{{ getStatusCount(status.code) }}</div>
          </div>

          <!-- Status Label -->
          <div class="status-label">{{ status.label }}</div>
        </div>

        <!-- Arrow (except for last item) -->
        <div v-if="index < statusFlow.length - 1" class="status-arrow">
          <v-icon>mdi-arrow-right</v-icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface StatusData {
  [key: string]: number;
}

const props = defineProps({
  statusData: {
    type: Object as PropType<StatusData>,
    default: () => ({})
  }
});

const emit = defineEmits(['status-click']);

// Status flow configuration matching the image
const statusFlow = [
  { code: 'VOID', label: 'ยกเลิก', color: 'red' },
  { code: 'PENDING', label: 'รอยืนรก', color: 'yellow' },
  { code: 'REQUEST', label: 'กำลังยืนรก', color: 'warning' },
  { code: 'IN_COMING', label: 'เริ่มรกแล้ว', color: 'warning' },
  { code: 'ON_GOING', label: 'รับของแล้ว', color: 'info' },
  { code: 'SUCCESS', label: 'ขนส่งสำเร็จ', color: 'success' },
];

function getStatusCount(statusCode: string): number {
  return props.statusData[statusCode] || 0;
}
</script>

<style scoped>
.status-flow-container {
  padding: 20px;
  overflow-x: auto;
}

.status-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 800px;
  gap: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s;
}

.status-item:hover {
  transform: scale(1.05);
}

.status-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: box-shadow 0.2s;
}

.status-circle:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.status-count {
  font-size: 24px;
  font-weight: bold;
}

.status-label {
  text-align: center;
  font-size: 14px;
  font-weight: medium;
  color: #424242;
  max-width: 100px;
  word-wrap: break-word;
}

.status-arrow {
  top: 35px;
  color: #9e9e9e;
  font-size: 24px;
}

.clickable {
  cursor: pointer;
}

@media (max-width: 768px) {
  .status-flow {
    flex-direction: column;
    gap: 30px;
  }

  .status-arrow {
    transform: rotate(90deg);
    right: auto;
    top: 90px;
  }
}
</style>
